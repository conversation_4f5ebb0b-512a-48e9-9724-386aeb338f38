import config from "./config";
import { fastify } from "fastify";
import type { JsonSchemaToTsProvider } from "@fastify/type-provider-json-schema-to-ts";
import compression from "@fastify/compress";
import { measures } from "@skywind-group/sw-utils";
import { getSecuredObjectData, SWError } from "@skywind-group/sw-wallet-adapter-core";
import * as sequelize from "sequelize";
import * as Errors from "./errors";
import logger from "./logger";
import { getSecuredUrl } from "./providers/utils";

const log = logger();

export type FastifyInstanceType = ReturnType<typeof create>;

export function create() {
    const app = fastify({
        bodyLimit: config.bodyParserJsonLimit
    }).withTypeProvider<JsonSchemaToTsProvider>();

    app.register(compression, {
        threshold: config.compressionThreshold
    });

    app.setNotFoundHandler((_req, res) => {
        const error = new Errors.ApiNotFoundError();
        res.code(error.responseStatus).send({
            code: error.code,
            message: error.message,
        });
    });

    app.setErrorHandler((err, req, res) => {
        if (err) {
            measures.measureProvider.saveError(err);
            if (res.sent) {
                log.warn(err, { request: req }, "Error headers sent");
            } else {
                let error: SWError;

                if (err?.validationContext === undefined && (err instanceof SWError || SWError.isSWError(err))) {
                    error = err as unknown as SWError;
                    const errorLevel = error.getErrorLevel ? error.getErrorLevel() : "error";
                    log[errorLevel](error, "SWError");
                } else if (err.validation) {
                    error = new Errors.ValidationError(err.message);
                } else if (err instanceof sequelize.ValidationError) {
                    log.warn(err, "Validation Error");
                    error = new Errors.ValidationError((err as sequelize.ValidationError).errors.map(item => item.message));
                } else if (err instanceof SyntaxError) {
                    const reason = err.message ? err.message : "N/A";
                    log.warn(err, `Malformed json - ${reason}`);
                    error = new Errors.MalformedJsonError(reason);
                } else {
                    log.error(err, "Internal error");
                    error = new Errors.InternalServerError(err);
                }

                res.code(error.responseStatus).send({
                    code: error.code,
                    message: error.message,
                });
            }
        }
    });

    app.addHook("preHandler", async ({ method, headers, url, query, body, ip }) => {
        if (url.endsWith("version") || url.endsWith("health")) {
            return;
        }
        let bodyData: ReturnType<typeof getSecuredObjectData> = undefined;
        if (body && ["POST", "PATCH", "PUT"].includes(method) && Object.keys(body).length > 0) {
            bodyData = getSecuredObjectData(Object.fromEntries(Object.entries(body)), config.logParams.secureKeys);
        }
        log.info({
            method,
            url: getSecuredUrl(url, config.logParams.secureKeys),
            query: getSecuredObjectData(Object.fromEntries(Object.entries(query)), config.logParams.secureKeys),
            ip,
            headers: {
                userAgent: headers["user-agent"] || "N/A",
                gs: headers["x-gs"] || "",
            },
            ...(bodyData ? { body: bodyData } : {})
        }, "Http request");
    });

    return app;
}
